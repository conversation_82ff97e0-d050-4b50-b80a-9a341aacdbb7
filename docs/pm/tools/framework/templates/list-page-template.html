<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知公告管理 - 列表</title>
    <!-- 注意：在路由环境下，CSS文件应在主页面(index.html)中统一加载 -->
    <!-- 如果直接访问此页面，请取消注释以下CSS链接 -->
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-list.css" />
    <style>
        /*
        数据表格链接样式规范：
        1. 数据列可点击文本：使用 <span onclick="..." class="table-link"> 避免浏览器默认样式冲突
        2. 操作列链接：使用 <a> 标签但添加 :not(.table-link) 选择器
        3. 统一使用主题色变量确保一致性
        */
        .data-table .table-link {
            color: var(--funi-primary-color, #007FFF) !important;
            cursor: pointer !important;
            text-decoration: none !important;
            border: none !important;
            outline: none !important;
            font-weight: normal !important;
            display: inline !important;
            background: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        .data-table .table-link:hover {
            color: var(--funi-primary-dark-color, #337ecc) !important;
            text-decoration: none !important;
        }

        .data-table .table-link:active,
        .data-table .table-link:focus {
            color: var(--funi-primary-color, #007FFF) !important;
            text-decoration: none !important;
            outline: none !important;
        }

        /* 操作列链接样式 - 仍使用a标签但确保主题色 */
        .data-table tbody td a:not(.table-link),
        .data-table td a:not(.table-link) {
            color: var(--funi-primary-color, #007FFF) !important;
            text-decoration: none !important;
            cursor: pointer !important;
        }
        .data-table tbody td a:not(.table-link):hover,
        .data-table td a:not(.table-link):hover {
            color: var(--funi-primary-dark-color, #337ecc) !important;
            text-decoration: none !important;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <div class="container-header">
            <!-- 1. 头部Tab切换 -->
            <div class="tabs">
                <div class="tab-item active" data-tab="all">全部</div>
                <div class="tab-item" data-tab="pending">待办</div>
                <div class="tab-item" data-tab="done">已办</div>
            </div>

            <!-- 2. 搜索区域 -->
            <div class="search-area collapsed"> <!-- Added 'collapsed' class -->
                <form class="search-form">
                    <div class="search-form-item">
                        <label for="organization">归属机构:</label>
                        <select id="organization" name="organization">
                            <option value="">请选择</option>
                            <option value="orgA">机构A</option>
                            <option value="orgB">机构B</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="customerName">客户名称:</label>
                        <input type="text" id="customerName" name="customerName" placeholder="请输入">
                    </div>
                    <div class="search-form-item">
                        <label for="customerManager">客户负责人:</label>
                        <select id="customerManager" name="customerManager">
                            <option value="">请选择</option>
                            <option value="managerA">负责人A</option>
                            <option value="managerB">负责人B</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="customerType">客户类别:</label>
                        <select id="customerType" name="customerType">
                            <option value="">请选择</option>
                            <option value="typeA">类别A</option>
                            <option value="typeB">类别B</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="customerSource">客户来源:</label>
                        <select id="customerSource" name="customerSource">
                            <option value="">请选择</option>
                            <option value="sourceA">来源A</option>
                            <option value="sourceB">来源B</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="customerNumber">客户编号:</label>
                        <input type="text" id="customerNumber" name="customerNumber" placeholder="请输入">
                    </div>
                    <div class="search-form-item">
                        <label for="approvalStatus">审批状态:</label>
                        <select id="approvalStatus" name="approvalStatus">
                            <option value="">请选择</option>
                            <option value="approved">通过</option>
                            <option value="pending">待审</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="approvalNumber">审批编号:</label>
                        <input type="text" id="approvalNumber" name="approvalNumber" placeholder="请输入">
                    </div>
                    <div class="search-form-item">
                        <label for="approvalType">审批类型:</label>
                        <select id="approvalType" name="approvalType">
                            <option value="">请选择</option>
                            <option value="typeA">类型A</option>
                            <option value="typeB">类型B</option>
                        </select>
                    </div>
                    <div class="search-form-item full-width">
                        <label for="createTimeStart">创建时间:</label>
                        <div class="date-range-picker">
                            <input type="date" id="createTimeStart" name="createTimeStart">
                            <span>~</span>
                            <input type="date" id="createTimeEnd" name="createTimeEnd">
                        </div>
                    </div>
                    <div class="search-form-item search-buttons-item">
                        <!-- Added search-form-item and search-buttons-item -->
                        <button type="button" class="button primary" id="queryButton">查询</button>
                        <button type="button" class="button" id="resetButton">重置</button>
                        <button type="button" class="button text" id="toggleCollapseButton">高级查询</button>
                        <!-- Changed text to '高级查询' -->
                    </div>
                </form>
            </div>
        </div>
        <div class="container-table">
            <!-- 操作按钮区域 -->
            <div class="action-buttons">
                <button class="button primary" onclick="window.addNew()">新建</button>
            </div>
            <!-- 3. 列表区域 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>审批编号</th>
                            <th>审批状态</th>
                            <th>归属机构</th>
                            <th>客户名称</th>
                            <th>客户类别</th>
                            <th>重要性</th>
                            <th>客户来源</th>
                            <th>审批类型</th>
                            <th>在办审批</th>
                            <th>客户负责人</th>
                            <th>更新日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Table rows will be inserted here by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <span>总共 <span id="totalItems">0</span> 条</span>
                <select id="pageSizeSelect">
                    <option value="10">10 条/页</option>
                    <option value="20">20 条/页</option>
                    <option value="50">50 条/页</option>
                    <option value="100">100 条/页</option>
                </select>
                <div class="page-buttons">
                    <button id="prevPageButton" disabled>上一页</button>
                    <span id="currentPageSpan">1</span>
                    <button id="nextPageButton">下一页</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        // 全局导航函数 - 必须在页面加载前定义，确保onclick属性能正确访问
        window.viewDetail = function(id) {
            // 动态获取当前模块路径
            const hash = window.top.location.hash;
            const pathParts = hash.split('/');
            const moduleName = pathParts[1] || 'default-module';
            window.top.location.hash = `#/${moduleName}/detail-review?id=${id}`;
        };

        window.editItem = function(id) {
            const hash = window.top.location.hash;
            const pathParts = hash.split('/');
            const moduleName = pathParts[1] || 'default-module';
            window.top.location.hash = `#/${moduleName}/add-edit?id=${id}`;
        };

        window.addNew = function() {
            const hash = window.top.location.hash;
            const pathParts = hash.split('/');
            const moduleName = pathParts[1] || 'default-module';
            window.top.location.hash = `#/${moduleName}/add-edit`;
        };

        // 操作按钮处理函数 - 使用onclick属性确保iframe环境兼容性
        window.handleViewAction = function(id) {
            window.viewDetail(id);
        };

        window.handleEditAction = function(id) {
            window.editItem(id);
        };

        window.handleDeleteAction = function(id) {
            if (confirm(`确定要删除编号为 ${id} 的记录吗？`)) {
                alert(`删除功能 - ID: ${id}`);
                // 实际项目中这里应该调用删除API
            }
        };

        window.handleAuditAction = function(id) {
            const hash = window.top.location.hash;
            const pathParts = hash.split('/');
            const moduleName = pathParts[1] || 'default-module';
            window.top.location.hash = `#/${moduleName}/detail-review?id=${id}&review=1`;
        };

        document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('.tab-item');
            const searchForm = document.querySelector('.search-form');
            const queryButton = document.getElementById('queryButton');
            const resetButton = document.getElementById('resetButton');
            const toggleCollapseButton = document.getElementById('toggleCollapseButton');
            const tableBody = document.getElementById('tableBody');
            const pageSizeSelect = document.getElementById('pageSizeSelect');
            const prevPageButton = document.getElementById('prevPageButton');
            const nextPageButton = document.getElementById('nextPageButton');
            const currentPageSpan = document.getElementById('currentPageSpan');
            const totalItemsSpan = document.getElementById('totalItems');

            // 检查必需的元素是否存在
            if (!tableBody || !pageSizeSelect || !queryButton || !resetButton || !toggleCollapseButton) {
                console.error('Required elements not found');
                return;
            }

            let activeTab = 'all';
            let isCollapsed = true; // Default to collapsed
            let currentPage = 1;
            let pageSize = parseInt(pageSizeSelect.value) || 10;

            // Initial state for the search area
            const searchArea = document.querySelector('.search-area');
            if (searchArea && toggleCollapseButton) {
                if (isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询';
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
            }

            const allTableData = [
                {
                    approvalNumber: '202504270000065',
                    approvalStatus: '审核通过',
                    organization: '集团本部',
                    customerName: 'CSCS',
                    customerType: '--',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '新增',
                    inProcessApproval: '--',
                    customerManager: '孟伟',
                    updateDate: '2025-04-27'
                },
                {
                    approvalNumber: '202504220000030',
                    approvalStatus: '审核通过',
                    organization: '集团本部',
                    customerName: '新增客户0422',
                    customerType: 'VIP客户',
                    importance: '重要',
                    customerSource: '--',
                    approvalType: '变更',
                    inProcessApproval: '--',
                    customerManager: '--',
                    updateDate: '2025-04-22'
                },
                {
                    approvalNumber: '202504220000022',
                    approvalStatus: '审核通过',
                    organization: '集团本部',
                    customerName: '0422',
                    customerType: '普通客户',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '变更',
                    inProcessApproval: '--',
                    customerManager: '顾友福',
                    updateDate: '2025-04-22'
                },
                {
                    approvalNumber: '202503120000101',
                    approvalStatus: '审核通过',
                    organization: '集团本部',
                    customerName: 'cs MMMM',
                    customerType: '--',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '变更',
                    inProcessApproval: '--',
                    customerManager: '孟伟',
                    updateDate: '2025-03-12'
                },
                {
                    approvalNumber: '202503120000041',
                    approvalStatus: '审核中',
                    organization: '房联云码科技',
                    customerName: '0110运维人员',
                    customerType: '--',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '变更',
                    inProcessApproval: '--',
                    customerManager: '--',
                    updateDate: '2025-03-12'
                },
                {
                    approvalNumber: '202501100000003',
                    approvalStatus: '审核通过',
                    organization: '成都信通有限公司',
                    customerName: '0110运维人员',
                    customerType: '--',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '新增',
                    inProcessApproval: '变更',
                    customerManager: '--',
                    updateDate: '2025-01-10'
                },
                {
                    approvalNumber: '202501100000002',
                    approvalStatus: '审核通过',
                    organization: '成都信通有限公司',
                    customerName: '0110客户',
                    customerType: '--',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '新增',
                    inProcessApproval: '--',
                    customerManager: '--',
                    updateDate: '2025-01-10'
                },
                {
                    approvalNumber: '202501080000097',
                    approvalStatus: '审核通过',
                    organization: '成都信通有限公司',
                    customerName: '0108客户 (测试)',
                    customerType: '--',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '新增',
                    inProcessApproval: '--',
                    customerManager: '--',
                    updateDate: '2025-01-08'
                },
                {
                    approvalNumber: '202501080000082',
                    approvalStatus: '审核通过',
                    organization: '成都信通有限公司',
                    customerName: '0108协同人',
                    customerType: '普通客户',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '新增',
                    inProcessApproval: '--',
                    customerManager: '--',
                    updateDate: '2025-01-08'
                },
                {
                    approvalNumber: '202501080000063',
                    approvalStatus: '审核通过',
                    organization: '成都信通有限公司',
                    customerName: '0108客户',
                    customerType: '普通客户',
                    importance: '重要',
                    customerSource: '--',
                    approvalType: '新增',
                    inProcessApproval: '--',
                    customerManager: '--',
                    updateDate: '2025-01-08'
                },
                // Add more dummy data for pagination testing
                {
                    approvalNumber: '202501070000001',
                    approvalStatus: '审核通过',
                    organization: '集团本部',
                    customerName: '测试客户1',
                    customerType: '普通客户',
                    importance: '--',
                    customerSource: '--',
                    approvalType: '新增',
                    inProcessApproval: '--',
                    customerManager: '张三',
                    updateDate: '2025-01-07'
                },
                {
                    approvalNumber: '202501070000002',
                    approvalStatus: '待审',
                    organization: '分公司A',
                    customerName: '测试客户2',
                    customerType: 'VIP客户',
                    importance: '重要',
                    customerSource: '线上',
                    approvalType: '变更',
                    inProcessApproval: '是',
                    customerManager: '李四',
                    updateDate: '2025-01-07'
                },
                {
                    approvalNumber: '202501070000003',
                    approvalStatus: '审核通过',
                    organization: '分公司B',
                    customerName: '测试客户3',
                    customerType: '普通客户',
                    importance: '--',
                    customerSource: '线下',
                    approvalType: '新增',
                    inProcessApproval: '--',
                    customerManager: '王五',
                    updateDate: '2025-01-07'
                },
                {
                    approvalNumber: '202501070000004',
                    approvalStatus: '审核通过',
                    organization: '集团本部',
                    customerName: '测试客户4',
                    customerType: 'VIP客户',
                    importance: '重要',
                    customerSource: '推荐',
                    approvalType: '变更',
                    inProcessApproval: '--',
                    customerManager: '赵六',
                    updateDate: '2025-01-07'
                },
                {
                    approvalNumber: '202501070000005',
                    approvalStatus: '待审',
                    organization: '分公司A',
                    customerName: '测试客户5',
                    customerType: '普通客户',
                    importance: '--',
                    customerSource: '线上',
                    approvalType: '新增',
                    inProcessApproval: '是',
                    customerManager: '钱七',
                    updateDate: '2025-01-07'
                }
            ];

            let filteredTableData = [...allTableData]; // Start with all data

            const renderTable = () => {
                tableBody.innerHTML = ''; // Clear existing rows
                const start = (currentPage - 1) * pageSize;
                const end = start + pageSize;
                const paginatedData = filteredTableData.slice(start, end);

                paginatedData.forEach(rowData => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${rowData.approvalNumber}</td>
                        <td>${rowData.approvalStatus}</td>
                        <td>${rowData.organization}</td>
                        <td><span onclick="window.viewDetail('${rowData.approvalNumber}')" class="table-link">${rowData.customerName}</span></td>
                        <td>${rowData.customerType}</td>
                        <td>${rowData.importance}</td>
                        <td>${rowData.customerSource}</td>
                        <td>${rowData.approvalType}</td>
                        <td>${rowData.inProcessApproval}</td>
                        <td>${rowData.customerManager}</td>
                        <td>${rowData.updateDate}</td>
                        <td>
                            <button type="button" class="button text" onclick="window.handleViewAction('${rowData.approvalNumber}')">详情</button>
                            <button type="button" class="button text" onclick="window.handleEditAction('${rowData.approvalNumber}')">编辑</button>
                            <button type="button" class="button text" onclick="window.handleDeleteAction('${rowData.approvalNumber}')">删除</button>
                            <button type="button" class="button text" onclick="window.handleAuditAction('${rowData.approvalNumber}')">审核</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                totalItemsSpan.textContent = filteredTableData.length;
                currentPageSpan.textContent = currentPage;
                prevPageButton.disabled = currentPage === 1;
                nextPageButton.disabled = currentPage * pageSize >= filteredTableData.length;
            };

            // handleAction函数已移除，现在使用onclick属性直接调用全局函数

            // Navigation functions already defined globally above

            // Event Listeners
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    activeTab = tab.dataset.tab;
                    console.log('Active Tab:', activeTab);
                    // In a real application, you would filter tableData based on the activeTab
                    // For this template, we'll just log the change.
                    currentPage = 1; // Reset page on tab change
                    renderTable();
                });
            });

            queryButton.addEventListener('click', () => {
                const formData = new FormData(searchForm);
                const searchParams = {};
                for (let [key, value] of formData.entries()) {
                    searchParams[key] = value;
                }
                console.log('查询条件:', searchParams);
                // Implement actual filtering logic here based on searchParams
                // For now, just re-render with current data
                filteredTableData = [...allTableData]; // Reset for demo
                currentPage = 1;
                renderTable();
            });

            resetButton.addEventListener('click', () => {
                searchForm.reset();
                console.log('重置搜索条件');
                filteredTableData = [...allTableData]; // Reset filtered data
                currentPage = 1;
                renderTable();
            });

            toggleCollapseButton.addEventListener('click', () => {
                const searchArea = document.querySelector('.search-area');
                isCollapsed = !isCollapsed;
                if (isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询'; // Changed text to '高级查询'
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
                console.log('Toggle collapse:', isCollapsed);
            });

            // 事件监听器已移除，现在使用onclick属性

            pageSizeSelect.addEventListener('change', (event) => {
                pageSize = parseInt(event.target.value);
                currentPage = 1; // Reset to first page when page size changes
                renderTable();
            });

            prevPageButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderTable();
                }
            });

            nextPageButton.addEventListener('click', () => {
                const totalPages = Math.ceil(filteredTableData.length / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderTable();
                }
            });

            // Initial render
            renderTable();
        });

        // 全局函数已在页面顶部定义
    </script>
</body>

</html>