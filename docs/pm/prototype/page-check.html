<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能页面质量检查工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .check-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .section-header {
            background: #007FFF;
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            cursor: pointer;
            user-select: none;
        }
        
        .section-header:hover {
            background: #0056b3;
        }
        
        .section-content {
            padding: 20px;
            display: none;
        }
        
        .section-content.active {
            display: block;
        }
        
        .check-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .check-item:hover {
            background: #f8f9fa;
        }
        
        .check-item input[type="checkbox"] {
            margin-right: 12px;
            margin-top: 2px;
            transform: scale(1.2);
        }
        
        .check-item.critical {
            border-left: 4px solid #dc3545;
        }
        
        .check-item.important {
            border-left: 4px solid #ffc107;
        }
        
        .check-item.recommended {
            border-left: 4px solid #28a745;
        }
        
        .check-label {
            flex: 1;
            cursor: pointer;
        }
        
        .priority-badge {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
            font-weight: 500;
        }
        
        .critical .priority-badge {
            background: #dc3545;
            color: white;
        }
        
        .important .priority-badge {
            background: #ffc107;
            color: #212529;
        }
        
        .recommended .priority-badge {
            background: #28a745;
            color: white;
        }
        
        .progress-bar {
            background: #e9ecef;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }
        
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        
        .btn {
            background: #007FFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }

        .page-selector {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .file-tree {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            background: #f8f9fa;
        }

        .file-item {
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 4px;
            margin: 2px 0;
        }

        .file-item:hover {
            background: #e9ecef;
        }

        .file-item.selected {
            background: #007FFF;
            color: white;
        }

        .folder {
            font-weight: 600;
            color: #495057;
        }

        .file {
            margin-left: 20px;
            color: #6c757d;
        }

        .analysis-result {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .issue-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }

        .issue-item.critical {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .issue-item.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }

        .issue-item.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .fix-suggestion {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
        }

        .copy-btn:hover {
            background: #218838;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>� 智能页面质量检查工具</h1>
        <p>自动扫描pages目录中的页面，检查iframe兼容性和CSS架构规范，生成修复建议</p>
    </div>

    <!-- 页面选择器 -->
    <div class="page-selector">
        <h3>📁 选择要检查的页面</h3>

        <!-- 方式1: 扫描目录 -->
        <div style="margin-bottom: 20px;">
            <h4>方式1: 自动扫描</h4>
            <div style="display: flex; gap: 20px; margin-bottom: 15px;">
                <button class="btn" onclick="scanPages()">🔄 扫描pages目录</button>
                <button class="btn" onclick="analyzeSelected()" id="analyzeBtn" disabled>🔍 分析选中页面</button>
            </div>

            <div class="file-tree" id="fileTree">
                <div class="loading">点击"扫描pages目录"开始...</div>
            </div>
        </div>

        <!-- 方式2: 手动输入 -->
        <div style="margin-bottom: 20px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <h4>方式2: 手动输入页面路径</h4>
            <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
                <input type="text" id="manualPath" placeholder="例如: pages/procurement-plan/list.html"
                       style="flex: 1; padding: 8px; border: 1px solid #e9ecef; border-radius: 4px;">
                <button class="btn" onclick="analyzeManualPath()">🔍 分析页面</button>
            </div>

            <!-- 快捷按钮 -->
            <div style="margin-bottom: 10px;">
                <strong>快捷选择：</strong>
                <button class="btn btn-secondary" onclick="quickSelect('pages/procurement-plan/list.html')" style="margin: 2px; padding: 4px 8px; font-size: 12px;">采购计划-列表</button>
                <button class="btn btn-secondary" onclick="quickSelect('pages/procurement-plan/add-edit.html')" style="margin: 2px; padding: 4px 8px; font-size: 12px;">采购计划-编辑</button>
                <button class="btn btn-secondary" onclick="quickSelect('pages/procurement-plan/detail-review.html')" style="margin: 2px; padding: 4px 8px; font-size: 12px;">采购计划-详情</button>
            </div>

            <div style="margin-top: 10px; font-size: 14px; color: #6c757d;">
                提示: 输入相对于当前目录的页面路径，或点击快捷按钮
            </div>
        </div>

        <div style="margin-top: 15px;">
            <strong>当前选中页面：</strong>
            <span id="selectedPage">未选择</span>
        </div>
    </div>

    <!-- 分析结果 -->
    <div class="analysis-result" id="analysisResult">
        <h3>📊 检查结果</h3>
        <div id="analysisContent"></div>

        <div class="fix-suggestion" id="fixSuggestion" style="display: none;">
            <h4>🛠️ AI修复建议（复制到AI对话中）：</h4>
            <div id="fixText"></div>
            <button class="copy-btn" onclick="copyFixSuggestion()">📋 复制修复建议</button>
        </div>
    </div>



    <script>
        let selectedPagePath = null;
        let pageContent = null;

        // 扫描pages目录
        async function scanPages() {
            const fileTree = document.getElementById('fileTree');
            fileTree.innerHTML = '<div class="loading">正在扫描目录...</div>';

            try {
                // 模拟扫描pages目录结构
                const pagesStructure = await mockScanPagesDirectory();
                renderFileTree(pagesStructure);
            } catch (error) {
                fileTree.innerHTML = '<div class="loading">扫描失败：' + error.message + '</div>';
            }
        }

        // 扫描pages目录（基于已知的页面结构）
        async function mockScanPagesDirectory() {
            // 模拟异步操作
            await new Promise(resolve => setTimeout(resolve, 500));

            // 基于实际存在的页面结构
            const knownPages = {
                'procurement-plan': ['list.html', 'add-edit.html', 'detail-review.html'],
                'supplier-management': ['list.html', 'add-edit.html', 'detail-review.html'],
                'contract-management': ['list.html', 'add-edit.html', 'detail-review.html'],
                'project-management': ['list.html', 'add-edit.html', 'detail-review.html']
            };

            // 验证页面是否实际存在
            const existingPages = {};
            for (const [folder, files] of Object.entries(knownPages)) {
                const existingFiles = [];
                for (const file of files) {
                    try {
                        const response = await fetch(`pages/${folder}/${file}`, { method: 'HEAD' });
                        if (response.ok) {
                            existingFiles.push(file);
                        }
                    } catch (error) {
                        // 文件不存在，跳过
                    }
                }
                if (existingFiles.length > 0) {
                    existingPages[folder] = existingFiles;
                }
            }

            return existingPages;
        }

        // 渲染文件树
        function renderFileTree(structure) {
            const fileTree = document.getElementById('fileTree');
            let html = '';

            for (const [folder, files] of Object.entries(structure)) {
                html += `<div class="folder">📁 ${folder}</div>`;
                files.forEach(file => {
                    const fullPath = `pages/${folder}/${file}`;
                    html += `<div class="file" onclick="selectPage('${fullPath}')">${file}</div>`;
                });
            }

            fileTree.innerHTML = html;
        }

        // 选择页面
        function selectPage(path) {
            // 移除之前的选中状态
            document.querySelectorAll('.file').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            event.target.classList.add('selected');

            selectedPagePath = path;
            document.getElementById('selectedPage').textContent = path;
            document.getElementById('analyzeBtn').disabled = false;
        }

        // 分析手动输入的页面路径
        function analyzeManualPath() {
            const manualPath = document.getElementById('manualPath').value.trim();
            if (!manualPath) {
                alert('请输入页面路径');
                return;
            }

            selectedPagePath = manualPath;
            document.getElementById('selectedPage').textContent = manualPath;

            // 清除文件树选中状态
            document.querySelectorAll('.file').forEach(item => {
                item.classList.remove('selected');
            });

            // 直接分析
            analyzeSelected();
        }

        // 快捷选择页面
        function quickSelect(path) {
            document.getElementById('manualPath').value = path;
            selectedPagePath = path;
            document.getElementById('selectedPage').textContent = path;

            // 清除文件树选中状态
            document.querySelectorAll('.file').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // 分析选中的页面
        async function analyzeSelected() {
            if (!selectedPagePath) {
                alert('请先选择一个页面');
                return;
            }

            const analysisResult = document.getElementById('analysisResult');
            const analysisContent = document.getElementById('analysisContent');

            analysisResult.style.display = 'block';
            analysisContent.innerHTML = '<div class="loading">正在分析页面...</div>';

            try {
                // 加载页面内容
                pageContent = await loadPageContent(selectedPagePath);

                // 执行检查
                const checkResults = performPageAnalysis(pageContent, selectedPagePath);

                // 显示结果
                renderAnalysisResults(checkResults);

                // 生成修复建议
                generateFixSuggestions(checkResults);

            } catch (error) {
                analysisContent.innerHTML = `<div class="issue-item critical">分析失败：${error.message}</div>`;
            }
        }

        // 加载页面内容
        async function loadPageContent(path) {
            try {
                const response = await fetch(path);
                if (!response.ok) {
                    throw new Error(`无法加载页面：${response.status}`);
                }
                return await response.text();
            } catch (error) {
                throw new Error(`加载页面失败：${error.message}`);
            }
        }

        // 执行页面分析
        function performPageAnalysis(content, path) {
            const results = {
                critical: [],
                warning: [],
                success: [],
                suggestions: []
            };

            // 检查1: JavaScript变量管理
            if (content.includes('let ') && content.includes('isInitialized')) {
                results.critical.push({
                    title: 'JavaScript变量重复声明风险',
                    description: '发现使用let声明的全局变量，在iframe环境中可能导致重复声明错误',
                    code: content.match(/let\s+\w+.*?;/g)?.[0] || 'let变量声明'
                });
            }

            if (content.includes('window.') && content.includes('pageState')) {
                results.success.push({
                    title: 'JavaScript状态管理正确',
                    description: '使用window对象管理状态，符合iframe兼容性要求'
                });
            } else {
                results.critical.push({
                    title: 'JavaScript状态管理不规范',
                    description: '未使用window对象管理全局状态，可能导致iframe环境中的变量冲突'
                });
            }

            // 检查2: CSS架构
            if (content.includes('<style>') && content.includes('业务') || content.includes('operation-log') || content.includes('log-item')) {
                results.success.push({
                    title: 'CSS架构规范正确',
                    description: '业务样式写在页面内部，符合架构要求'
                });
            } else {
                results.warning.push({
                    title: 'CSS架构需要检查',
                    description: '请确认业务特定样式是否写在页面内部的<style>标签中'
                });
            }

            // 检查3: 初始化模式
            const hasMultipleInit = content.includes('DOMContentLoaded') &&
                                  content.includes('window.addEventListener') &&
                                  content.includes('setTimeout');

            if (hasMultipleInit) {
                results.success.push({
                    title: '初始化模式完整',
                    description: '提供了多种初始化时机，确保iframe兼容性'
                });
            } else {
                results.warning.push({
                    title: '初始化模式不完整',
                    description: '建议提供多种初始化时机（DOMContentLoaded + window.load + setTimeout）'
                });
            }

            // 检查4: 事件绑定方式
            if (content.includes('onclick=') && !content.includes('addEventListener')) {
                results.success.push({
                    title: '事件绑定方式正确',
                    description: '使用onclick属性，在iframe环境中更稳定'
                });
            } else if (content.includes('addEventListener')) {
                results.warning.push({
                    title: '事件绑定方式需要优化',
                    description: '建议在iframe环境中优先使用onclick属性而非addEventListener'
                });
            }

            return results;
        }

        // 渲染分析结果
        function renderAnalysisResults(results) {
            const analysisContent = document.getElementById('analysisContent');
            let html = '';

            // 关键问题
            if (results.critical.length > 0) {
                html += '<h4>🚨 关键问题（必须修复）</h4>';
                results.critical.forEach(issue => {
                    html += `
                        <div class="issue-item critical">
                            <strong>${issue.title}</strong><br>
                            ${issue.description}
                            ${issue.code ? `<div class="code-example">${issue.code}</div>` : ''}
                        </div>
                    `;
                });
            }

            // 警告
            if (results.warning.length > 0) {
                html += '<h4>⚠️ 警告（建议修复）</h4>';
                results.warning.forEach(issue => {
                    html += `
                        <div class="issue-item warning">
                            <strong>${issue.title}</strong><br>
                            ${issue.description}
                        </div>
                    `;
                });
            }

            // 成功项目
            if (results.success.length > 0) {
                html += '<h4>✅ 检查通过</h4>';
                results.success.forEach(issue => {
                    html += `
                        <div class="issue-item success">
                            <strong>${issue.title}</strong><br>
                            ${issue.description}
                        </div>
                    `;
                });
            }

            if (html === '') {
                html = '<div class="issue-item">未发现明显问题，但建议进行手动检查。</div>';
            }

            analysisContent.innerHTML = html;
        }

        // 生成修复建议
        function generateFixSuggestions(results) {
            const fixSuggestion = document.getElementById('fixSuggestion');
            const fixText = document.getElementById('fixText');

            if (results.critical.length === 0 && results.warning.length === 0) {
                fixSuggestion.style.display = 'none';
                return;
            }

            let suggestions = `请修复页面 ${selectedPagePath} 中的以下问题：\n\n`;

            if (results.critical.length > 0) {
                suggestions += "🚨 关键问题（必须修复）：\n";
                results.critical.forEach((issue, index) => {
                    suggestions += `${index + 1}. ${issue.title}\n`;
                    suggestions += `   问题：${issue.description}\n`;

                    if (issue.title.includes('变量重复声明')) {
                        suggestions += `   解决方案：将全局变量改为使用window对象管理\n`;
                        suggestions += `   示例：window.pageState = window.pageState || { isInitialized: false };\n`;
                    }

                    if (issue.title.includes('状态管理')) {
                        suggestions += `   解决方案：使用window对象管理状态，添加状态重置函数\n`;
                        suggestions += `   示例：\n`;
                        suggestions += `   window.pageState = window.pageState || { isInitialized: false };\n`;
                        suggestions += `   function resetPageState() { window.pageState.isInitialized = false; }\n`;
                    }

                    suggestions += `\n`;
                });
            }

            if (results.warning.length > 0) {
                suggestions += "⚠️ 建议优化：\n";
                results.warning.forEach((issue, index) => {
                    suggestions += `${index + 1}. ${issue.title}\n`;
                    suggestions += `   建议：${issue.description}\n`;

                    if (issue.title.includes('初始化模式')) {
                        suggestions += `   解决方案：添加多种初始化时机\n`;
                        suggestions += `   示例：\n`;
                        suggestions += `   if (document.readyState === 'loading') {\n`;
                        suggestions += `       document.addEventListener('DOMContentLoaded', initPageData);\n`;
                        suggestions += `   } else { initPageData(); }\n`;
                        suggestions += `   window.addEventListener('load', initPageData);\n`;
                        suggestions += `   setTimeout(initPageData, 100);\n`;
                    }

                    if (issue.title.includes('事件绑定')) {
                        suggestions += `   解决方案：优先使用onclick属性\n`;
                        suggestions += `   示例：<button onclick="handleClick()">按钮</button>\n`;
                    }

                    suggestions += `\n`;
                });
            }

            suggestions += "请按照以上建议修复页面，确保iframe环境兼容性。";

            fixText.textContent = suggestions;
            fixSuggestion.style.display = 'block';
        }

        // 复制修复建议
        function copyFixSuggestion() {
            const fixText = document.getElementById('fixText');
            navigator.clipboard.writeText(fixText.textContent).then(() => {
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '✅ 已复制';
                btn.style.background = '#28a745';

                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#28a745';
                }, 2000);
            }).catch(err => {
                alert('复制失败，请手动复制文本');
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后的初始化
        });
    </script>
</body>
</html>
