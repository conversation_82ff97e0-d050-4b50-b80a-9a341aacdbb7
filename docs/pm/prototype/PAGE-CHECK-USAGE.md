# 智能页面质量检查工具使用说明

## 🎯 工具概述

`page-check.html` 是一个智能的页面质量检查工具，可以自动分析页面代码，检查iframe兼容性和CSS架构规范，并生成可直接复制给AI的修复建议。

## 📍 访问地址

```
http://localhost:8080/page-check.html
```

## 🚀 使用流程

### 步骤1: 选择页面

有两种方式选择要检查的页面：

#### 方式1: 自动扫描（推荐）
1. 点击 **"🔄 扫描pages目录"** 按钮
2. 工具会自动扫描 `pages/` 目录下的所有页面
3. 在文件树中点击要检查的页面文件
4. 点击 **"🔍 分析选中页面"** 按钮

#### 方式2: 手动输入
1. 在输入框中输入页面路径，例如：`pages/procurement-plan/list.html`
2. 或点击快捷按钮选择常用页面
3. 点击 **"🔍 分析页面"** 按钮

### 步骤2: 查看检查结果

工具会自动分析页面并显示：

- **🚨 关键问题（必须修复）**: 影响iframe兼容性的严重问题
- **⚠️ 警告（建议修复）**: 可能影响稳定性的问题
- **✅ 检查通过**: 符合规范的项目

### 步骤3: 复制修复建议

1. 查看 **"🛠️ AI修复建议"** 部分
2. 点击 **"📋 复制修复建议"** 按钮
3. 将复制的文本粘贴到AI对话中
4. AI会根据建议自动修复页面问题

## 🔍 检查项目说明

### 关键检查项（必须通过）

#### 1. JavaScript变量管理
- **检查内容**: 是否使用window对象管理全局状态
- **问题示例**: `let isInitialized = false;` (会导致重复声明错误)
- **正确示例**: `window.pageState = window.pageState || { isInitialized: false };`

#### 2. JavaScript状态管理
- **检查内容**: 是否有状态重置机制
- **要求**: 必须包含状态重置函数并在初始化时调用

#### 3. CSS架构规范
- **检查内容**: 业务样式是否写在页面内部
- **要求**: 业务特定样式必须写在HTML页面的`<style>`标签中

### 重要检查项（建议通过）

#### 1. 初始化模式
- **检查内容**: 是否提供多种初始化时机
- **要求**: 包含DOMContentLoaded、window.load、setTimeout等多种时机

#### 2. 事件绑定方式
- **检查内容**: 是否优先使用onclick属性
- **要求**: 在iframe环境中onclick比addEventListener更稳定

## 📋 修复建议格式

工具生成的修复建议包含：

1. **问题描述**: 详细说明发现的问题
2. **解决方案**: 具体的修复方法
3. **代码示例**: 正确的代码写法
4. **优先级**: 关键问题 vs 建议优化

### 修复建议示例

```
请修复页面 pages/procurement-plan/list.html 中的以下问题：

🚨 关键问题（必须修复）：
1. JavaScript变量重复声明风险
   问题：发现使用let声明的全局变量，在iframe环境中可能导致重复声明错误
   解决方案：将全局变量改为使用window对象管理
   示例：window.pageState = window.pageState || { isInitialized: false };

⚠️ 建议优化：
1. 初始化模式不完整
   建议：建议提供多种初始化时机（DOMContentLoaded + window.load + setTimeout）
   解决方案：添加多种初始化时机
   示例：
   if (document.readyState === 'loading') {
       document.addEventListener('DOMContentLoaded', initPageData);
   } else { initPageData(); }
   window.addEventListener('load', initPageData);
   setTimeout(initPageData, 100);

请按照以上建议修复页面，确保iframe环境兼容性。
```

## 🎯 使用场景

### 1. 新页面生成后检查
- 每个新生成的页面都应该用此工具检查
- 确保符合iframe兼容性要求

### 2. 页面修改后验证
- 对现有页面进行修改后的质量验证
- 确保修改没有引入新问题

### 3. 问题排查
- 当页面在iframe环境中出现问题时
- 快速定位可能的原因

### 4. 批量检查
- 定期对所有页面进行质量检查
- 确保整体代码质量

## 💡 使用技巧

### 1. 快捷操作
- 使用快捷按钮快速选择常用页面
- 复制修复建议后直接粘贴给AI

### 2. 结果解读
- 优先修复"关键问题"，这些会影响页面正常运行
- "警告"项目建议修复，提高页面稳定性
- "检查通过"项目表示符合规范

### 3. 修复流程
1. 复制修复建议
2. 粘贴给AI助手
3. AI自动修复页面
4. 重新检查验证修复效果

## 🚨 注意事项

### 1. 网络要求
- 工具需要能够访问pages目录下的文件
- 确保本地服务器正常运行

### 2. 检查限制
- 工具主要检查JavaScript和CSS的基础规范
- 复杂的业务逻辑问题需要人工检查

### 3. 修复建议
- 修复建议是基于常见问题模式生成的
- 具体修复时可能需要根据实际情况调整

## 🔧 故障排除

### 问题1: 无法加载页面
- **原因**: 页面路径错误或文件不存在
- **解决**: 检查路径是否正确，确保文件存在

### 问题2: 扫描目录失败
- **原因**: 网络问题或权限问题
- **解决**: 使用手动输入方式，或检查服务器状态

### 问题3: 复制功能不工作
- **原因**: 浏览器安全限制
- **解决**: 手动选择文本复制，或使用HTTPS访问

## 📈 工具价值

### 对产品经理
- **一键检查**: 无需技术背景即可检查页面质量
- **自动修复**: 复制建议给AI即可完成修复
- **质量保证**: 确保所有页面符合技术规范

### 对开发团队
- **标准化**: 统一的质量检查标准
- **效率提升**: 自动化检查减少人工工作
- **问题预防**: 提前发现潜在问题

这个工具让页面质量检查变得简单高效，产品经理只需要点击几下就能完成专业的技术质量检查。

## 📚 参考文档

- [iframe环境兼容性完整指南](../tools/framework/IFRAME-COMPATIBILITY-GUIDE.md)
- [页面生成提示词](../tools/prompts/core/generate-page.md)
