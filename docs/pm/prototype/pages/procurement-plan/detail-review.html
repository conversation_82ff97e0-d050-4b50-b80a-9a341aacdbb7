<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 详情/审核</title>
    <!-- 注意：在路由环境下，CSS文件应在主页面(index.html)中统一加载 -->
    <!-- 如果直接访问此页面，请取消注释以下CSS链接 -->
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-form.css" />

    <!-- 页面专用样式 -->
    <style>
        /* ===== 操作日志组件样式 ===== */
        /* 注意：由于iframe环境中CSS类样式可能不生效，实际使用内联样式 */
        .operation-log {
            padding: 16px 0 !important;
        }

        .log-list {
            display: flex !important;
            flex-direction: column !important;
            gap: 16px !important;
        }

        .log-item {
            background: #ffffff !important;
            border: 1px solid #DCDFE6 !important;
            border-radius: 8px !important;
            padding: 16px !important;
            transition: all 0.2s ease !important;
            margin-bottom: 16px !important;
        }

        .log-item:hover {
            border-color: #007FFF !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .log-header {
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
            margin-bottom: 12px !important;
            padding-bottom: 8px !important;
            border-bottom: 1px solid #E4E7ED !important;
        }

        .log-action {
            font-weight: 600 !important;
            color: #007FFF !important;
            background: #F2F6FC !important;
            padding: 4px 8px !important;
            border-radius: 4px !important;
            font-size: 14px !important;
            display: inline-block !important;
        }

        .log-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            background: #EBEEF5;
            color: #606266;
        }

        .log-time {
            margin-left: auto;
            font-size: 14px;
            color: #909399;
        }

        .log-content {
            color: #303133;
        }

        .log-operator {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
        }

        .log-comment {
            line-height: 1.5;
            color: #303133;
        }

        .no-data {
            text-align: center;
            padding: 40px 20px;
            color: #909399;
            font-size: 14px;
        }
    </style>
</head>

<body>

    <div id="app" class="form-container">
        <div class="detail-header">
            <div class="header-left">
                <div class="header-title">采购计划管理 - 详情</div>
            </div>
            <div class="header-right">
                <div class="approval-info">
                    <span>计划编号: <span id="displayPlanNumber">CG-20240801-001</span></span>
                    <span>审核状态: <span id="displayAuditStatus">待审核</span></span>
                </div>
            </div>
        </div>

        <div class="funi-tabs">
            <div class="tab-item active" data-tab="basic-info">基本信息</div>
            <div class="tab-item" data-tab="operation-log">流程记录</div>
        </div>

        <div class="container-content">
            <div class="form-step-content form-step-1 active" data-tab-content="basic-info">
                <div class="form-section-title">招标信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="planNumber" class="form-item-label">计划编号:</label>
                            <div class="form-item-value">
                                <div id="planNumber" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="planProjectName" class="form-item-label">计划项目名称:</label>
                            <div class="form-item-value">
                                <div id="planProjectName" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="auditStatus" class="form-item-label">审核状态:</label>
                            <div class="form-item-value">
                                <div id="auditStatus" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementType" class="form-item-label">采购类型:</label>
                            <div class="form-item-value">
                                <div id="procurementType" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementMethod" class="form-item-label">采购方式:</label>
                            <div class="form-item-value">
                                <div id="procurementMethod" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingCategory" class="form-item-label">招标类别:</label>
                            <div class="form-item-value">
                                <div id="biddingCategory" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="budgetAmount" class="form-item-label">采购预算金额（万元）:</label>
                            <div class="form-item-value">
                                <div id="budgetAmount" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="fundSource" class="form-item-label">资金来源:</label>
                            <div class="form-item-value">
                                <div id="fundSource" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingTime" class="form-item-label">招标时间:</label>
                            <div class="form-item-value">
                                <div id="biddingTime" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementOrganizationMethod" class="form-item-label">采购组织方式:</label>
                            <div class="form-item-value">
                                <div id="procurementOrganizationMethod" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementAgency" class="form-item-label">采购代理机构:</label>
                            <div class="form-item-value">
                                <div id="procurementAgency" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementContact" class="form-item-label">采购联系人:</label>
                            <div class="form-item-value">
                                <div id="procurementContact" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="contactPhone" class="form-item-label">联系电话:</label>
                            <div class="form-item-value">
                                <div id="contactPhone" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementDescription" class="form-item-label">采购内容描述:</label>
                            <div class="form-item-value">
                                <div id="procurementDescription" class="display-value"></div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="form-section-title">项目信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="projectCode" class="form-item-label">项目编码:</label>
                            <div class="form-item-value">
                                <div id="projectCode" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectType" class="form-item-label">项目类型:</label>
                            <div class="form-item-value">
                                <div id="projectType" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectPriority" class="form-item-label">项目优先级:</label>
                            <div class="form-item-value">
                                <div id="projectPriority" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="expectedStartDate" class="form-item-label">预计开始时间:</label>
                            <div class="form-item-value">
                                <div id="expectedStartDate" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="expectedEndDate" class="form-item-label">预计结束时间:</label>
                            <div class="form-item-value">
                                <div id="expectedEndDate" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectManager" class="form-item-label">项目负责人:</label>
                            <div class="form-item-value">
                                <div id="projectManager" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="department" class="form-item-label">申请部门:</label>
                            <div class="form-item-value">
                                <div id="department" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectBackground" class="form-item-label">项目背景:</label>
                            <div class="form-item-value">
                                <div id="projectBackground" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectObjective" class="form-item-label">项目目标:</label>
                            <div class="form-item-value">
                                <div id="projectObjective" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="remarks" class="form-item-label">备注说明:</label>
                            <div class="form-item-value">
                                <div id="remarks" class="display-value"></div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="form-section-title">附件信息</div>
                <div class="funi-form">
                    <div class="form-item-row">
                        <label class="form-item-label">相关附件:</label>
                        <div class="form-item-value">
                            <div id="attachmentList" class="attachment-list">
                                <!-- 附件列表将由JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核区域 -->
                <div id="reviewSection" class="form-section-title" style="display: none;">
                    <div class="form-section-title">审核意见</div>
                    <div class="funi-form">
                        <form class="form-grid">
                            <div class="form-item-row">
                                <label for="reviewResult" class="form-item-label required">审核结果:</label>
                                <div class="form-item-value">
                                    <select id="reviewResult" name="reviewResult">
                                        <option value="">请选择</option>
                                        <option value="通过">通过</option>
                                        <option value="驳回">驳回</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-item-row">
                                <label for="reviewComment" class="form-item-label required">审核意见:</label>
                                <div class="form-item-value">
                                    <textarea id="reviewComment" name="reviewComment" rows="4" placeholder="请输入审核意见"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="form-step-content" data-tab-content="operation-log">
                <div class="form-section-title">流程记录</div>
                <div style="padding: 16px 0;">
                    <div id="operationLogList" style="display: flex; flex-direction: column; gap: 16px;">
                        <!-- 流程记录将由JavaScript动态生成 -->
                        <!-- 静态内容作为备用 -->
                        <div style="background: #ffffff; border: 1px solid #DCDFE6; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #E4E7ED;">
                                <span style="font-weight: 600; color: #007FFF; background: #F2F6FC; padding: 4px 8px; border-radius: 4px; font-size: 14px;">创建</span>
                                <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; background: #EBEEF5; color: #606266;">草稿</span>
                                <span style="margin-left: auto; font-size: 14px; color: #909399;">2024-08-01 10:00:00</span>
                            </div>
                            <div style="color: #303133;">
                                <div style="font-size: 14px; color: #909399; margin-bottom: 8px;">操作人：张三</div>
                                <div style="line-height: 1.5; color: #303133;">创建采购计划</div>
                            </div>
                        </div>
                        <div style="background: #ffffff; border: 1px solid #DCDFE6; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #E4E7ED;">
                                <span style="font-weight: 600; color: #007FFF; background: #F2F6FC; padding: 4px 8px; border-radius: 4px; font-size: 14px;">提交审核</span>
                                <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; background: #EBEEF5; color: #606266;">待审核</span>
                                <span style="margin-left: auto; font-size: 14px; color: #909399;">2024-08-01 14:30:00</span>
                            </div>
                            <div style="color: #303133;">
                                <div style="font-size: 14px; color: #909399; margin-bottom: 8px;">操作人：张三</div>
                                <div style="line-height: 1.5; color: #303133;">提交审核，请审核人员及时处理</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按钮区域 -->
        <div class="form-actions" id="formActions">
            <!-- 按钮将由JavaScript动态生成 -->
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        // 使用window对象避免重复声明错误
        window.reviewPageState = window.reviewPageState || {
            isInitialized: false,
            currentId: null,
            isReviewMode: false,
            currentTab: 'basic-info'
        };

        // 重置页面状态函数
        function resetReviewPageState() {
            window.reviewPageState.isInitialized = false;
            window.reviewPageState.currentId = null;
            window.reviewPageState.isReviewMode = false;
            window.reviewPageState.currentTab = 'basic-info';
        }

        function initPageData() {
            // 每次进入页面时重置状态，避免缓存问题
            resetReviewPageState();
            window.reviewPageState.isInitialized = true;

            // 获取URL参数
            const hash = window.top.location.hash;
            const queryString = hash.split('?')[1] || '';
            const urlParams = new URLSearchParams(queryString);
            window.reviewPageState.currentId = urlParams.get('id');
            window.reviewPageState.isReviewMode = urlParams.get('review') === '1';

            // 设置页面标题
            const titleElement = document.querySelector('title');
            const headerTitle = document.querySelector('.header-title');
            if (titleElement && headerTitle) {
                const title = window.reviewPageState.isReviewMode ? '采购计划管理 - 审核' : '采购计划管理 - 详情';
                titleElement.textContent = title;
                headerTitle.textContent = title;
            }

            // 加载数据
            if (window.reviewPageState.currentId) {
                loadDetailData(window.reviewPageState.currentId);
            }

            // 初始化Tab切换
            initTabSwitching();

            // 初始化按钮
            updateButtons();

            // 显示/隐藏审核区域
            const reviewSection = document.getElementById('reviewSection');
            if (reviewSection) {
                reviewSection.style.display = window.reviewPageState.isReviewMode ? 'block' : 'none';
            }
        }

        function loadDetailData(id) {
            // 模拟从服务器加载数据
            const mockData = {
                planNumber: 'CG-20240801-001',
                planProjectName: '办公设备采购计划',
                auditStatus: '待审核',
                procurementType: '货物',
                procurementMethod: '公告比选',
                biddingCategory: '设备采购',
                budgetAmount: '50.00',
                fundSource: '自有资金',
                biddingTime: '2024年9月',
                procurementOrganizationMethod: '自行组织',
                procurementAgency: '',
                procurementContact: '张三',
                contactPhone: '13800138000',
                procurementDescription: '办公设备采购，包括电脑、打印机等',
                projectCode: 'PRJ-2024-001',
                projectType: '设备采购',
                projectPriority: '中',
                expectedStartDate: '2024-09-01',
                expectedEndDate: '2024-12-31',
                projectManager: '李四',
                department: '采购部',
                projectBackground: '为提升办公效率，需要更新办公设备',
                projectObjective: '采购高质量的办公设备，提升工作效率',
                remarks: '优先考虑节能环保产品'
            };

            // 填充显示数据
            Object.keys(mockData).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = mockData[key] || '--';
                }
            });

            // 更新头部显示信息
            const displayPlanNumber = document.getElementById('displayPlanNumber');
            const displayAuditStatus = document.getElementById('displayAuditStatus');
            if (displayPlanNumber) displayPlanNumber.textContent = mockData.planNumber;
            if (displayAuditStatus) displayAuditStatus.textContent = mockData.auditStatus;

            // 加载附件列表
            loadAttachments();

            // 加载操作日志
            loadOperationLog();
        }

        function loadAttachments() {
            const attachmentList = document.getElementById('attachmentList');
            if (!attachmentList) return;

            // 模拟附件数据
            const mockAttachments = [
                { name: '采购需求说明书.pdf', size: '2.5MB', uploadTime: '2024-08-01 10:30' },
                { name: '预算明细表.xlsx', size: '1.2MB', uploadTime: '2024-08-01 10:35' },
                { name: '技术规格书.docx', size: '3.1MB', uploadTime: '2024-08-01 10:40' }
            ];

            if (mockAttachments.length === 0) {
                attachmentList.innerHTML = '<div class="no-data">暂无附件</div>';
                return;
            }

            attachmentList.innerHTML = mockAttachments.map(file => `
                <div class="attachment-item">
                    <iconify-icon icon="mdi:file-document-outline"></iconify-icon>
                    <div class="attachment-info">
                        <div class="attachment-name">${file.name}</div>
                        <div class="attachment-meta">${file.size} | ${file.uploadTime}</div>
                    </div>
                    <button type="button" class="button text" onclick="downloadFile('${file.name}')">下载</button>
                </div>
            `).join('');
        }

        function loadOperationLog() {
            const operationLogList = document.getElementById('operationLogList');
            if (!operationLogList) return;

            // 模拟操作日志数据
            const mockLogs = [
                {
                    action: '创建',
                    operator: '张三',
                    time: '2024-08-01 10:00:00',
                    comment: '创建采购计划',
                    status: '草稿'
                },
                {
                    action: '提交审核',
                    operator: '张三',
                    time: '2024-08-01 14:30:00',
                    comment: '提交审核，请审核人员及时处理',
                    status: '待审核'
                }
            ];

            if (mockLogs.length === 0) {
                operationLogList.innerHTML = '<div class="no-data">暂无操作记录</div>';
                return;
            }

            const htmlContent = mockLogs.map(log => `
                <div style="background: #ffffff; border: 1px solid #DCDFE6; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #E4E7ED;">
                        <span style="font-weight: 600; color: #007FFF; background: #F2F6FC; padding: 4px 8px; border-radius: 4px; font-size: 14px;">${log.action}</span>
                        <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; background: #EBEEF5; color: #606266;">${log.status}</span>
                        <span style="margin-left: auto; font-size: 14px; color: #909399;">${log.time}</span>
                    </div>
                    <div style="color: #303133;">
                        <div style="font-size: 14px; color: #909399; margin-bottom: 8px;">操作人：${log.operator}</div>
                        <div style="line-height: 1.5; color: #303133;">${log.comment}</div>
                    </div>
                </div>
            `).join('');

            operationLogList.innerHTML = htmlContent;
        }

        function initTabSwitching() {
            const tabItems = document.querySelectorAll('.tab-item');
            const tabContents = document.querySelectorAll('[data-tab-content]');

            tabItems.forEach(tab => {
                tab.addEventListener('click', () => {
                    const targetTab = tab.dataset.tab;

                    // 更新tab状态
                    tabItems.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');

                    // 更新内容显示
                    tabContents.forEach(content => {
                        const isTarget = content.dataset.tabContent === targetTab;
                        content.classList.toggle('active', isTarget);
                        content.style.display = isTarget ? 'block' : 'none';
                    });

                    window.reviewPageState.currentTab = targetTab;
                    updateButtons();
                });
            });

            // 初始化显示状态
            tabContents.forEach(content => {
                const isActive = content.dataset.tabContent === window.reviewPageState.currentTab;
                content.classList.toggle('active', isActive);
                content.style.display = isActive ? 'block' : 'none';
            });
        }

        function updateButtons() {
            const formActions = document.getElementById('formActions');
            if (!formActions) return;

            formActions.innerHTML = '';

            // 返回按钮
            const backButton = document.createElement('button');
            backButton.type = 'button';
            backButton.className = 'button';
            backButton.textContent = '返回';
            backButton.onclick = () => {
                window.top.location.hash = '#/procurement-plan';
            };
            formActions.appendChild(backButton);

            // 编辑按钮（非审核模式且在基本信息tab）
            if (!window.reviewPageState.isReviewMode && window.reviewPageState.currentTab === 'basic-info') {
                const editButton = document.createElement('button');
                editButton.type = 'button';
                editButton.className = 'button';
                editButton.textContent = '编辑';
                editButton.onclick = () => {
                    window.top.location.hash = `#/procurement-plan/add-edit?id=${window.reviewPageState.currentId}`;
                };
                formActions.appendChild(editButton);
            }

            // 审核按钮（审核模式且在基本信息tab）
            if (window.reviewPageState.isReviewMode && window.reviewPageState.currentTab === 'basic-info') {
                const submitReviewButton = document.createElement('button');
                submitReviewButton.type = 'button';
                submitReviewButton.className = 'button primary';
                submitReviewButton.textContent = '提交审核';
                submitReviewButton.onclick = submitReview;
                formActions.appendChild(submitReviewButton);
            }
        }

        function submitReview() {
            const reviewResult = document.getElementById('reviewResult');
            const reviewComment = document.getElementById('reviewComment');

            if (!reviewResult || !reviewComment) return;

            if (!reviewResult.value) {
                alert('请选择审核结果');
                reviewResult.focus();
                return;
            }

            if (!reviewComment.value.trim()) {
                alert('请输入审核意见');
                reviewComment.focus();
                return;
            }

            // 模拟提交审核
            const reviewData = {
                id: window.reviewPageState.currentId,
                result: reviewResult.value,
                comment: reviewComment.value.trim(),
                reviewer: '当前用户',
                reviewTime: new Date().toISOString()
            };

            console.log('提交审核:', reviewData);
            alert(`审核${reviewResult.value}！`);

            // 返回列表页
            window.top.location.hash = '#/procurement-plan';
        }

        function downloadFile(fileName) {
            // 模拟文件下载
            alert(`下载文件: ${fileName}`);
        }

        // 多种初始化时机确保兼容性
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initPageData);
        } else {
            initPageData();
        }

        // iframe环境额外初始化
        window.addEventListener('load', initPageData);
        setTimeout(initPageData, 100);
    </script>
</body>

</html>
