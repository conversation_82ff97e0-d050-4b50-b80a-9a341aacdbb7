<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 新增/编辑</title>
    <!-- 注意：在路由环境下，CSS文件应在主页面(index.html)中统一加载 -->
    <!-- 如果直接访问此页面，请取消注释以下CSS链接 -->
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-form.css" />
</head>

<body>

    <div id="app" class="form-container">
        <div class="form-step-container">
            <div class="form-step active">
                <div class="step-number">1</div>
                <div class="step-title">基本信息</div>
            </div>
            <iconify-icon icon="ic:outline-navigate-next" class="funi-step-icon"></iconify-icon>
            <div class="form-step">
                <div class="step-number">2</div>
                <div class="step-title">项目信息</div>
            </div>
            <iconify-icon icon="ic:outline-navigate-next" class="funi-step-icon"></iconify-icon>
            <div class="form-step">
                <div class="step-number">3</div>
                <div class="step-title">附件上传</div>
            </div>
        </div>
        <div class="container-content">
            <div class="form-step-content form-step-1 active">
                <div class="form-section-title">招标信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="planNumber" class="form-item-label">计划编号:</label>
                            <div class="form-item-value">
                                <input type="text" id="planNumber" name="planNumber" value="自动生成" readonly>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementType" class="form-item-label required">采购类型:</label>
                            <div class="form-item-value">
                                <select id="procurementType" name="procurementType">
                                    <option value="">请选择</option>
                                    <option value="施工">施工</option>
                                    <option value="货物">货物</option>
                                    <option value="服务">服务</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingCategory" class="form-item-label required">招标类别:</label>
                            <div class="form-item-value">
                                <select id="biddingCategory" name="biddingCategory">
                                    <option value="">请选择</option>
                                    <option value="工程建设">工程建设</option>
                                    <option value="设备采购">设备采购</option>
                                    <option value="服务外包">服务外包</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementMethod" class="form-item-label required">采购方式:</label>
                            <div class="form-item-value">
                                <select id="procurementMethod" name="procurementMethod">
                                    <option value="">请选择</option>
                                    <option value="公告比选">公告比选</option>
                                    <option value="邀请比选">邀请比选</option>
                                    <option value="竞争性磋商">竞争性磋商</option>
                                    <option value="竞争性谈判">竞争性谈判</option>
                                    <option value="询价择优">询价择优</option>
                                    <option value="单一来源">单一来源</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="budgetAmount" class="form-item-label required">采购预算金额（万元）:</label>
                            <div class="form-item-value">
                                <input type="number" id="budgetAmount" name="budgetAmount" placeholder="采购预算金额" step="0.01">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="fundSource" class="form-item-label required">资金来源:</label>
                            <div class="form-item-value">
                                <select id="fundSource" name="fundSource">
                                    <option value="">请选择</option>
                                    <option value="自有资金">自有资金</option>
                                    <option value="政府投资">政府投资</option>
                                    <option value="其它社会资本">其它社会资本</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingTime" class="form-item-label">招标时间:</label>
                            <div class="form-item-value">
                                <input type="text" id="biddingTime" name="biddingTime" placeholder="请输入" maxlength="50">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementOrganizationMethod" class="form-item-label">采购组织方式:</label>
                            <div class="form-item-value">
                                <select id="procurementOrganizationMethod" name="procurementOrganizationMethod">
                                    <option value="">请选择</option>
                                    <option value="自行组织">自行组织</option>
                                    <option value="委托代理">委托代理</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementAgency" class="form-item-label">采购代理机构:</label>
                            <div class="form-item-value">
                                <input type="text" id="procurementAgency" name="procurementAgency" placeholder="请输入采购代理机构">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementContact" class="form-item-label">采购联系人:</label>
                            <div class="form-item-value">
                                <input type="text" id="procurementContact" name="procurementContact" placeholder="请输入采购联系人">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="contactPhone" class="form-item-label">联系电话:</label>
                            <div class="form-item-value">
                                <input type="tel" id="contactPhone" name="contactPhone" placeholder="请输入联系电话">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementDescription" class="form-item-label">采购内容描述:</label>
                            <div class="form-item-value">
                                <textarea id="procurementDescription" name="procurementDescription" rows="4" placeholder="请输入采购内容描述"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="form-step-content form-step-2">
                <div class="form-section-title">项目信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="planProjectName" class="form-item-label required">计划项目名称:</label>
                            <div class="form-item-value">
                                <input type="text" id="planProjectName" name="planProjectName" placeholder="请输入计划项目名称">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectCode" class="form-item-label">项目编码:</label>
                            <div class="form-item-value">
                                <input type="text" id="projectCode" name="projectCode" placeholder="请输入项目编码">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectType" class="form-item-label">项目类型:</label>
                            <div class="form-item-value">
                                <select id="projectType" name="projectType">
                                    <option value="">请选择</option>
                                    <option value="基础设施">基础设施</option>
                                    <option value="信息化">信息化</option>
                                    <option value="设备采购">设备采购</option>
                                    <option value="服务采购">服务采购</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectPriority" class="form-item-label">项目优先级:</label>
                            <div class="form-item-value">
                                <select id="projectPriority" name="projectPriority">
                                    <option value="">请选择</option>
                                    <option value="高">高</option>
                                    <option value="中">中</option>
                                    <option value="低">低</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="expectedStartDate" class="form-item-label">预计开始时间:</label>
                            <div class="form-item-value">
                                <input type="date" id="expectedStartDate" name="expectedStartDate">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="expectedEndDate" class="form-item-label">预计结束时间:</label>
                            <div class="form-item-value">
                                <input type="date" id="expectedEndDate" name="expectedEndDate">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectManager" class="form-item-label">项目负责人:</label>
                            <div class="form-item-value">
                                <input type="text" id="projectManager" name="projectManager" placeholder="请输入项目负责人">
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="department" class="form-item-label">申请部门:</label>
                            <div class="form-item-value">
                                <select id="department" name="department">
                                    <option value="">请选择</option>
                                    <option value="采购部">采购部</option>
                                    <option value="工程部">工程部</option>
                                    <option value="信息部">信息部</option>
                                    <option value="行政部">行政部</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectBackground" class="form-item-label">项目背景:</label>
                            <div class="form-item-value">
                                <textarea id="projectBackground" name="projectBackground" rows="4" placeholder="请输入项目背景"></textarea>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectObjective" class="form-item-label">项目目标:</label>
                            <div class="form-item-value">
                                <textarea id="projectObjective" name="projectObjective" rows="4" placeholder="请输入项目目标"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="form-step-content form-step-3">
                <div class="form-section-title">附件上传</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="attachments" class="form-item-label">相关附件:</label>
                            <div class="form-item-value">
                                <div class="file-upload-area">
                                    <input type="file" id="attachments" name="attachments" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.png">
                                    <div class="upload-hint">
                                        <iconify-icon icon="mdi:cloud-upload-outline"></iconify-icon>
                                        <p>点击上传或拖拽文件到此区域</p>
                                        <p class="upload-note">支持 PDF、Word、Excel、图片格式，单个文件不超过10MB</p>
                                    </div>
                                </div>
                                <div id="fileList" class="file-list">
                                    <!-- 上传的文件列表将显示在这里 -->
                                </div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="remarks" class="form-item-label">备注说明:</label>
                            <div class="form-item-value">
                                <textarea id="remarks" name="remarks" rows="4" placeholder="请输入备注说明"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 按钮区域 - 将由JavaScript动态生成 -->
        <div class="form-actions" id="formActions">
            <!-- 按钮将由JavaScript动态生成 -->
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        // 使用window对象避免重复声明错误
        window.pageState = window.pageState || {
            isInitialized: false,
            currentStep: 1,
            isEditMode: false,
            editId: null
        };

        // 重置页面状态函数
        function resetPageState() {
            window.pageState.isInitialized = false;
            window.pageState.currentStep = 1;
            window.pageState.isEditMode = false;
            window.pageState.editId = null;
        }

        function initPageData() {
            // 每次进入页面时重置状态，避免缓存问题
            resetPageState();
            window.pageState.isInitialized = true;

            // 获取URL参数
            const hash = window.top.location.hash;
            const queryString = hash.split('?')[1] || '';
            const urlParams = new URLSearchParams(queryString);
            window.pageState.editId = urlParams.get('id');
            window.pageState.isEditMode = !!window.pageState.editId;

            // 设置页面标题
            const titleElement = document.querySelector('title');
            if (titleElement) {
                titleElement.textContent = window.pageState.isEditMode ? '采购计划管理 - 编辑' : '采购计划管理 - 新增';
            }

            // 如果是编辑模式，加载数据
            if (window.pageState.isEditMode) {
                loadEditData(window.pageState.editId);
            }

            // 初始化步骤导航
            initStepNavigation();

            // 初始化按钮
            updateButtons();

            // 初始化文件上传
            initFileUpload();

            // 初始化表单验证
            initFormValidation();
        }

        function loadEditData(id) {
            // 模拟从服务器加载数据
            const mockData = {
                planNumber: 'CG-20240801-001',
                procurementType: '货物',
                biddingCategory: '设备采购',
                procurementMethod: '公告比选',
                budgetAmount: '50.00',
                fundSource: '自有资金',
                biddingTime: '2024年9月',
                procurementOrganizationMethod: '自行组织',
                procurementAgency: '',
                procurementContact: '张三',
                contactPhone: '13800138000',
                procurementDescription: '办公设备采购，包括电脑、打印机等',
                planProjectName: '办公设备采购计划',
                projectCode: 'PRJ-2024-001',
                projectType: '设备采购',
                projectPriority: '中',
                expectedStartDate: '2024-09-01',
                expectedEndDate: '2024-12-31',
                projectManager: '李四',
                department: '采购部',
                projectBackground: '为提升办公效率，需要更新办公设备',
                projectObjective: '采购高质量的办公设备，提升工作效率',
                remarks: '优先考虑节能环保产品'
            };

            // 填充表单数据
            Object.keys(mockData).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = mockData[key];
                }
            });
        }

        function initStepNavigation() {
            const steps = document.querySelectorAll('.form-step');
            const stepContents = document.querySelectorAll('.form-step-content');

            steps.forEach((step, index) => {
                step.addEventListener('click', () => {
                    if (index + 1 <= window.pageState.currentStep || validateCurrentStep()) {
                        goToStep(index + 1);
                    }
                });
            });
        }

        function goToStep(step) {
            const steps = document.querySelectorAll('.form-step');
            const stepContents = document.querySelectorAll('.form-step-content');

            // 更新步骤状态
            steps.forEach((s, index) => {
                s.classList.toggle('active', index + 1 === step);
                s.classList.toggle('completed', index + 1 < step);
            });

            // 更新内容显示
            stepContents.forEach((content, index) => {
                content.classList.toggle('active', index + 1 === step);
            });

            window.pageState.currentStep = step;
            updateButtons();
        }

        function validateCurrentStep() {
            const currentContent = document.querySelector(`.form-step-${window.pageState.currentStep}`);
            if (!currentContent) return true;

            const requiredFields = currentContent.querySelectorAll('[required], .required input, .required select');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });

            if (!isValid) {
                alert('请填写必填字段');
            }

            return isValid;
        }

        function updateButtons() {
            const formActions = document.getElementById('formActions');
            if (!formActions) return;

            formActions.innerHTML = '';

            // 返回按钮
            const backButton = document.createElement('button');
            backButton.type = 'button';
            backButton.className = 'button';
            backButton.textContent = '返回';
            backButton.onclick = () => {
                window.top.location.hash = '#/procurement-plan';
            };
            formActions.appendChild(backButton);

            // 上一步按钮
            if (window.pageState.currentStep > 1) {
                const prevButton = document.createElement('button');
                prevButton.type = 'button';
                prevButton.className = 'button';
                prevButton.textContent = '上一步';
                prevButton.onclick = () => goToStep(window.pageState.currentStep - 1);
                formActions.appendChild(prevButton);
            }

            // 下一步/保存按钮
            if (window.pageState.currentStep < 3) {
                const nextButton = document.createElement('button');
                nextButton.type = 'button';
                nextButton.className = 'button primary';
                nextButton.textContent = '下一步';
                nextButton.onclick = () => {
                    if (validateCurrentStep()) {
                        goToStep(window.pageState.currentStep + 1);
                    }
                };
                formActions.appendChild(nextButton);
            } else {
                const saveButton = document.createElement('button');
                saveButton.type = 'button';
                saveButton.className = 'button primary';
                saveButton.textContent = isEditMode ? '保存' : '提交';
                saveButton.onclick = saveForm;
                formActions.appendChild(saveButton);
            }
        }

        function initFileUpload() {
            const fileInput = document.getElementById('attachments');
            const fileList = document.getElementById('fileList');
            const uploadArea = document.querySelector('.file-upload-area');

            if (!fileInput || !fileList || !uploadArea) return;

            // 点击上传区域触发文件选择
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择处理
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            handleFiles(files);
        }

        function handleFiles(files) {
            const fileList = document.getElementById('fileList');
            if (!fileList) return;

            Array.from(files).forEach(file => {
                if (file.size > 10 * 1024 * 1024) { // 10MB limit
                    alert(`文件 ${file.name} 超过10MB限制`);
                    return;
                }

                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <iconify-icon icon="mdi:file-document-outline"></iconify-icon>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${(file.size / 1024).toFixed(1)}KB</span>
                    <button type="button" class="button text" onclick="removeFile(this)">删除</button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        function removeFile(button) {
            const fileItem = button.closest('.file-item');
            if (fileItem) {
                fileItem.remove();
            }
        }

        function initFormValidation() {
            // 预算金额验证
            const budgetAmount = document.getElementById('budgetAmount');
            if (budgetAmount) {
                budgetAmount.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    if (value < 0) {
                        e.target.value = '';
                        alert('预算金额不能为负数');
                    }
                });
            }

            // 日期验证
            const startDate = document.getElementById('expectedStartDate');
            const endDate = document.getElementById('expectedEndDate');

            if (startDate && endDate) {
                const validateDates = () => {
                    if (startDate.value && endDate.value && startDate.value > endDate.value) {
                        alert('开始时间不能晚于结束时间');
                        endDate.value = '';
                    }
                };

                startDate.addEventListener('change', validateDates);
                endDate.addEventListener('change', validateDates);
            }
        }

        function saveForm() {
            // 验证所有步骤
            for (let i = 1; i <= 3; i++) {
                window.pageState.currentStep = i;
                if (!validateCurrentStep()) {
                    goToStep(i);
                    return;
                }
            }

            // 收集表单数据
            const formData = new FormData();
            const inputs = document.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                if (input.name && input.value) {
                    formData.append(input.name, input.value);
                }
            });

            // 模拟保存
            console.log('保存表单数据:', Object.fromEntries(formData));

            const action = isEditMode ? '更新' : '创建';
            alert(`${action}成功！`);

            // 返回列表页
            window.top.location.hash = '#/procurement-plan';
        }

        // 多种初始化时机确保兼容性
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initPageData);
        } else {
            initPageData();
        }

        // iframe环境额外初始化
        window.addEventListener('load', initPageData);
        setTimeout(initPageData, 100);
    </script>
</body>

</html>
