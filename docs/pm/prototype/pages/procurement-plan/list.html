<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 列表</title>
    <!-- 注意：在路由环境下，CSS文件应在主页面(index.html)中统一加载 -->
    <!-- 如果直接访问此页面，请取消注释以下CSS链接 -->
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-list.css" />
    <style>
        /*
        ===== 业务页面专用样式 =====
        根据CSS架构规范，业务页面的特定样式写在页面内部的<style>标签中
        这确保了样式的正确隔离和iframe环境兼容性

        数据表格链接样式规范：
        1. 数据列可点击文本：使用 <span onclick="..." class="table-link"> 避免浏览器默认样式冲突
        2. 操作列链接：使用 <a> 标签但添加 :not(.table-link) 选择器
        3. 统一使用主题色变量确保一致性
        */
        .data-table .table-link {
            color: var(--funi-primary-color, #007FFF) !important;
            cursor: pointer !important;
            text-decoration: none !important;
            border: none !important;
            outline: none !important;
            font-weight: normal !important;
            display: inline !important;
            background: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        .data-table .table-link:hover {
            color: var(--funi-primary-dark-color, #337ecc) !important;
            text-decoration: none !important;
        }

        .data-table .table-link:active,
        .data-table .table-link:focus {
            color: var(--funi-primary-color, #007FFF) !important;
            text-decoration: none !important;
            outline: none !important;
        }

        /* 操作列链接样式 - 仍使用a标签但确保主题色 */
        .data-table tbody td a:not(.table-link),
        .data-table td a:not(.table-link) {
            color: var(--funi-primary-color, #007FFF) !important;
            text-decoration: none !important;
            cursor: pointer !important;
        }
        .data-table tbody td a:not(.table-link):hover,
        .data-table td a:not(.table-link):hover {
            color: var(--funi-primary-dark-color, #337ecc) !important;
            text-decoration: none !important;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <div class="container-header">
            <!-- 1. 头部Tab切换 -->
            <div class="tabs">
                <div class="tab-item active" data-tab="all">全部</div>
                <div class="tab-item" data-tab="pending">待办</div>
                <div class="tab-item" data-tab="done">已办</div>
            </div>

            <!-- 2. 搜索区域 -->
            <div class="search-area collapsed"> <!-- Added 'collapsed' class -->
                <form class="search-form">
                    <div class="search-form-item">
                        <label for="planNumber">计划编号:</label>
                        <input type="text" id="planNumber" name="planNumber" placeholder="请输入计划编号">
                    </div>
                    <div class="search-form-item">
                        <label for="planProjectName">计划项目名称:</label>
                        <input type="text" id="planProjectName" name="planProjectName" placeholder="请输入项目名称">
                    </div>
                    <div class="search-form-item">
                        <label for="procurementType">采购类型:</label>
                        <select id="procurementType" name="procurementType">
                            <option value="">请选择</option>
                            <option value="施工">施工</option>
                            <option value="货物">货物</option>
                            <option value="服务">服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementMethod">采购方式:</label>
                        <select id="procurementMethod" name="procurementMethod">
                            <option value="">请选择</option>
                            <option value="公告比选">公告比选</option>
                            <option value="邀请比选">邀请比选</option>
                            <option value="竞争性磋商">竞争性磋商</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="询价择优">询价择优</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="auditStatus">审核状态:</label>
                        <select id="auditStatus" name="auditStatus">
                            <option value="">请选择</option>
                            <option value="待审核">待审核</option>
                            <option value="审核中">审核中</option>
                            <option value="审核通过">审核通过</option>
                            <option value="审核驳回">审核驳回</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="fundSource">资金来源:</label>
                        <select id="fundSource" name="fundSource">
                            <option value="">请选择</option>
                            <option value="自有资金">自有资金</option>
                            <option value="政府投资">政府投资</option>
                            <option value="其它社会资本">其它社会资本</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="budgetAmountMin">预算金额（万元）:</label>
                        <div class="range-input">
                            <input type="number" id="budgetAmountMin" name="budgetAmountMin" placeholder="最小值">
                            <span>~</span>
                            <input type="number" id="budgetAmountMax" name="budgetAmountMax" placeholder="最大值">
                        </div>
                    </div>
                    <div class="search-form-item full-width">
                        <label for="createTimeStart">创建时间:</label>
                        <div class="date-range-picker">
                            <input type="date" id="createTimeStart" name="createTimeStart">
                            <span>~</span>
                            <input type="date" id="createTimeEnd" name="createTimeEnd">
                        </div>
                    </div>
                    <div class="search-form-item search-buttons-item">
                        <!-- Added search-form-item and search-buttons-item -->
                        <button type="button" class="button primary" id="queryButton">查询</button>
                        <button type="button" class="button" id="resetButton">重置</button>
                        <button type="button" class="button text" id="toggleCollapseButton">高级查询</button>
                        <!-- Changed text to '高级查询' -->
                    </div>
                </form>
            </div>
        </div>
        <div class="container-table">
            <!-- 操作按钮区域 -->
            <div class="action-buttons">
                <button class="button primary" onclick="window.addNew()">新建</button>
            </div>
            <!-- 3. 列表区域 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>计划编号</th>
                            <th>计划项目名称</th>
                            <th>采购类型</th>
                            <th>采购方式</th>
                            <th>预算金额（万元）</th>
                            <th>资金来源</th>
                            <th>审核状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Table rows will be inserted here by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <span>总共 <span id="totalItems">0</span> 条</span>
                <select id="pageSizeSelect">
                    <option value="10">10 条/页</option>
                    <option value="20">20 条/页</option>
                    <option value="50">50 条/页</option>
                    <option value="100">100 条/页</option>
                </select>
                <div class="page-buttons">
                    <button id="prevPageButton" disabled>上一页</button>
                    <span id="currentPageSpan">1</span>
                    <button id="nextPageButton">下一页</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        // ===== iframe环境兼容的状态管理 =====
        window.procurementListState = window.procurementListState || {
            isInitialized: false,
            activeTab: 'all',
            isCollapsed: true,
            currentPage: 1,
            pageSize: 10,
            filteredTableData: [],
            allTableData: []
        };

        // 页面状态重置函数
        function resetProcurementListState() {
            window.procurementListState.isInitialized = false;
            window.procurementListState.activeTab = 'all';
            window.procurementListState.isCollapsed = true;
            window.procurementListState.currentPage = 1;
            window.procurementListState.pageSize = 10;
            window.procurementListState.filteredTableData = [];
            window.procurementListState.allTableData = [];
        }

        // 全局导航函数 - 必须在页面加载前定义
        window.viewDetail = function(id) {
            window.top.location.hash = `#/procurement-plan/detail-review?id=${id}`;
        };

        window.editItem = function(id) {
            window.top.location.hash = `#/procurement-plan/add-edit?id=${id}`;
        };

        window.addNew = function() {
            window.top.location.hash = `#/procurement-plan/add-edit`;
        };

        // 测试函数
        window.testButtonClick = function() {
            console.log('Test button click function called');
            alert('测试按钮点击成功！');
        };

        // 操作按钮处理函数
        window.handleViewAction = function(id) {
            console.log('View action for ID:', id);
            window.viewDetail(id);
        };

        window.handleEditAction = function(id) {
            console.log('Edit action for ID:', id);
            window.editItem(id);
        };

        window.handleDeleteAction = function(id) {
            console.log('Delete action for ID:', id);
            if (confirm(`确定要删除编号为 ${id} 的采购计划吗？`)) {
                alert(`删除功能 - ID: ${id}`);
            }
        };

        window.handleAuditAction = function(id) {
            console.log('Audit action for ID:', id);
            window.top.location.hash = `#/procurement-plan/detail-review?id=${id}&review=1`;
        };

        // 页面初始化函数
        function initProcurementListPage() {
            // 防止重复初始化
            if (window.procurementListState.isInitialized) {
                return;
            }

            // 每次进入页面时重置状态，避免缓存问题
            resetProcurementListState();
            window.procurementListState.isInitialized = true;

            const tabs = document.querySelectorAll('.tab-item');
            const searchForm = document.querySelector('.search-form');
            const queryButton = document.getElementById('queryButton');
            const resetButton = document.getElementById('resetButton');
            const toggleCollapseButton = document.getElementById('toggleCollapseButton');
            const tableBody = document.getElementById('tableBody');
            const pageSizeSelect = document.getElementById('pageSizeSelect');
            const prevPageButton = document.getElementById('prevPageButton');
            const nextPageButton = document.getElementById('nextPageButton');
            const currentPageSpan = document.getElementById('currentPageSpan');
            const totalItemsSpan = document.getElementById('totalItems');

            // 检查必需的元素是否存在
            if (!tableBody || !pageSizeSelect || !queryButton || !resetButton || !toggleCollapseButton) {
                console.error('Required elements not found');
                return;
            }

            // 使用状态管理对象
            window.procurementListState.pageSize = parseInt(pageSizeSelect.value) || 10;

            // Initial state for the search area
            const searchArea = document.querySelector('.search-area');
            if (searchArea && toggleCollapseButton) {
                if (window.procurementListState.isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询';
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
            }

            // 初始化数据到状态管理对象
            window.procurementListState.allTableData = [
                {
                    planNumber: 'CG-20240801-001',
                    planProjectName: '办公设备采购计划',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    budgetAmount: '50.00',
                    fundSource: '自有资金',
                    auditStatus: '审核通过',
                    createTime: '2024-08-01'
                },
                {
                    planNumber: 'CG-20240802-002',
                    planProjectName: '办公楼装修工程',
                    procurementType: '施工',
                    procurementMethod: '邀请比选',
                    budgetAmount: '200.00',
                    fundSource: '政府投资',
                    auditStatus: '审核中',
                    createTime: '2024-08-02'
                },
                {
                    planNumber: 'CG-20240803-003',
                    planProjectName: '物业管理服务',
                    procurementType: '服务',
                    procurementMethod: '竞争性磋商',
                    budgetAmount: '120.00',
                    fundSource: '自有资金',
                    auditStatus: '待审核',
                    createTime: '2024-08-03'
                },
                {
                    planNumber: 'CG-20240804-004',
                    planProjectName: '信息系统开发',
                    procurementType: '服务',
                    procurementMethod: '竞争性谈判',
                    budgetAmount: '300.00',
                    fundSource: '其它社会资本',
                    auditStatus: '审核通过',
                    createTime: '2024-08-04'
                },
                {
                    planNumber: 'CG-20240805-005',
                    planProjectName: '车辆采购计划',
                    procurementType: '货物',
                    procurementMethod: '询价择优',
                    budgetAmount: '80.00',
                    fundSource: '自有资金',
                    auditStatus: '审核驳回',
                    createTime: '2024-08-05'
                }
            ];

            window.procurementListState.filteredTableData = [...window.procurementListState.allTableData]; // Start with all data

            const renderTable = () => {
                const state = window.procurementListState;
                console.log('Rendering table with data:', state.filteredTableData.length, 'items');
                tableBody.innerHTML = ''; // Clear existing rows
                const start = (state.currentPage - 1) * state.pageSize;
                const end = start + state.pageSize;
                const paginatedData = state.filteredTableData.slice(start, end);

                paginatedData.forEach(rowData => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${rowData.planNumber}</td>
                        <td><span onclick="window.viewDetail('${rowData.planNumber}')" class="table-link">${rowData.planProjectName}</span></td>
                        <td>${rowData.procurementType}</td>
                        <td>${rowData.procurementMethod}</td>
                        <td>${rowData.budgetAmount}</td>
                        <td>${rowData.fundSource}</td>
                        <td>${rowData.auditStatus}</td>
                        <td>${rowData.createTime}</td>
                        <td>
                            <button type="button" class="button text" onclick="window.handleViewAction('${rowData.planNumber}')">详情</button>
                            <button type="button" class="button text" onclick="window.handleEditAction('${rowData.planNumber}')">编辑</button>
                            <button type="button" class="button text" onclick="window.handleDeleteAction('${rowData.planNumber}')">删除</button>
                            <button type="button" class="button text" onclick="window.handleAuditAction('${rowData.planNumber}')">审核</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                console.log('Table rendered successfully');

                totalItemsSpan.textContent = state.filteredTableData.length;
                currentPageSpan.textContent = state.currentPage;
                prevPageButton.disabled = state.currentPage === 1;
                nextPageButton.disabled = state.currentPage * state.pageSize >= state.filteredTableData.length;
            };

            // handleAction函数已移除，现在使用onclick属性直接调用全局函数

            // Navigation functions already defined globally above

            // Event Listeners
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    window.procurementListState.activeTab = tab.dataset.tab;
                    console.log('Active Tab:', window.procurementListState.activeTab);
                    // In a real application, you would filter tableData based on the activeTab
                    // For this template, we'll just log the change.
                    window.procurementListState.currentPage = 1; // Reset page on tab change
                    renderTable();
                });
            });

            queryButton.addEventListener('click', () => {
                const formData = new FormData(searchForm);
                const searchParams = {};
                for (let [key, value] of formData.entries()) {
                    searchParams[key] = value;
                }
                console.log('查询条件:', searchParams);
                // Implement actual filtering logic here based on searchParams
                // For now, just re-render with current data
                window.procurementListState.filteredTableData = [...window.procurementListState.allTableData]; // Reset for demo
                window.procurementListState.currentPage = 1;
                renderTable();
            });

            resetButton.addEventListener('click', () => {
                searchForm.reset();
                console.log('重置搜索条件');
                window.procurementListState.filteredTableData = [...window.procurementListState.allTableData]; // Reset filtered data
                window.procurementListState.currentPage = 1;
                renderTable();
            });

            toggleCollapseButton.addEventListener('click', () => {
                const searchArea = document.querySelector('.search-area');
                window.procurementListState.isCollapsed = !window.procurementListState.isCollapsed;
                if (window.procurementListState.isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询'; // Changed text to '高级查询'
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
                console.log('Toggle collapse:', window.procurementListState.isCollapsed);
            });

            // 事件监听器已移除，现在使用onclick属性

            pageSizeSelect.addEventListener('change', (event) => {
                window.procurementListState.pageSize = parseInt(event.target.value);
                window.procurementListState.currentPage = 1; // Reset to first page when page size changes
                renderTable();
            });

            prevPageButton.addEventListener('click', () => {
                if (window.procurementListState.currentPage > 1) {
                    window.procurementListState.currentPage--;
                    renderTable();
                }
            });

            nextPageButton.addEventListener('click', () => {
                const totalPages = Math.ceil(window.procurementListState.filteredTableData.length / window.procurementListState.pageSize);
                if (window.procurementListState.currentPage < totalPages) {
                    window.procurementListState.currentPage++;
                    renderTable();
                }
            });

            // Initial render
            renderTable();
        }

        // 多种初始化时机确保iframe环境兼容性
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initProcurementListPage);
        } else {
            initProcurementListPage();
        }
        window.addEventListener('load', initProcurementListPage);
        setTimeout(initProcurementListPage, 100);
    </script>
</body>

</html>
